<script lang="ts">
    import {CalendarDate, type DateValue, getLocalTimeZone, today} from '@internationalized/date';
    import {onMount} from 'svelte';
    import LanguagesApiClient from '$lib/api/LanguagesApiClient';
    import type {CreateReadingActivityData, UpdateReadingActivityData} from '$lib/api/ReadingActivitiesApiClient';
    import ReadingActivitiesApiClient from '$lib/api/ReadingActivitiesApiClient';
    import * as Tooltip from '$lib/components/shadcn/ui/tooltip';
    import InformationCircleSvg from '$lib/components/svg/InformationCircleSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import DatePicker from '$lib/components/ui/DatePicker.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import NumberInput from '$lib/components/ui/NumberInput.svelte';
    import Select from '$lib/components/ui/Select.svelte';
    import type Book from '$lib/domain/Book';
    import type Language from '$lib/domain/Language';
    import type ReadingActivity from '$lib/domain/ReadingActivity';
    import {t} from '$lib/localization/Localization';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';

    export let open = false;
    export let book: Book | null = null;
    export let editingActivity: ReadingActivity | null = null;
    export let isEditing = false;
    export let modalTriggeredByStatusChange = false;
    export let onActivitySaved: (() => void) | undefined = undefined;

    let selectedLanguage: Language | null = null;
    let startDate: DateValue | undefined;
    let endDate: DateValue | undefined;
    let pagesRead = '';
    let totalPages = '';
    let validationErrors: Record<string, string> = {};
    let languageOptions: Language[] = [];
    // let isEndDateDisabled = false;

    $: isEndDateDisabled = Boolean(totalPages) && (!pagesRead || parseInt(pagesRead) < parseInt(totalPages));
    $: if (isEndDateDisabled && endDate) {
        endDate = undefined;
    }

    onMount(() => {
        if (book === null) {
            return;
        }

        loadLanguages();
        setupLanguageOptions();
    });

    $: if (open) {
        if (isEditing && editingActivity) {
            populateFormFromActivity(editingActivity);
        } else {
            resetForm();
        }
    }

    async function loadLanguages() {
        const languagesApiClient = new LanguagesApiClient();
        const response = await languagesApiClient.index();

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });
            return;
        }

        languageOptions = response.data.map((language) => ({
            ...language,
            label: language.text
        }));
    }

    function setupLanguageOptions() {
        if (book.languages.length > 0) {
            selectedLanguage = book.languages[0];
            selectedLanguage.label = selectedLanguage.text;
        }

        if (book.numberOfPages) {
            totalPages = book.numberOfPages.toString();
        }
    }

    function resetForm() {
        selectedLanguage = book.languages.length > 0 ? book.languages[0] : null;
        if (selectedLanguage !== null) {
            selectedLanguage.label = selectedLanguage.text;
        }

        startDate = today(getLocalTimeZone());
        endDate = undefined;
        pagesRead = '';
        totalPages = book.numberOfPages ? book.numberOfPages.toString() : '';
        validationErrors = {};

        // Force recalculation of isEndDateDisabled after resetting values
        const totalPagesNum = totalPages ? parseInt(totalPages) : 0;
        const pagesReadNum = pagesRead ? parseInt(pagesRead) : 0;
        isEndDateDisabled = totalPagesNum > 0 && (pagesReadNum === 0 || pagesReadNum < totalPagesNum);
    }

    function populateFormFromActivity(activity: ReadingActivity) {
        selectedLanguage = activity.language || null;
        if (selectedLanguage !== null) {
            selectedLanguage.label = selectedLanguage.text;
        }

        if (activity.startDate) {
            const startDateObj = new Date(activity.startDate);
            startDate = new CalendarDate(startDateObj.getFullYear(), startDateObj.getMonth() + 1, startDateObj.getDate());
        } else {
            startDate = undefined;
        }

        if (activity.endDate) {
            const endDateObj = new Date(activity.endDate);
            endDate = new CalendarDate(endDateObj.getFullYear(), endDateObj.getMonth() + 1, endDateObj.getDate());
        } else {
            endDate = undefined;
        }

        pagesRead = activity.pagesRead ? activity.pagesRead.toString() : '';
        totalPages = activity.totalPages ? activity.totalPages.toString() : '';
        validationErrors = {};

        // Force recalculation of isEndDateDisabled after setting values
        const totalPagesNum = totalPages ? parseInt(totalPages) : 0;
        const pagesReadNum = pagesRead ? parseInt(pagesRead) : 0;
        isEndDateDisabled = totalPagesNum > 0 && (pagesReadNum === 0 || pagesReadNum < totalPagesNum);
        console.log('PopulateForm: pagesRead=', pagesRead, 'totalPages=', totalPages, 'isEndDateDisabled=', isEndDateDisabled);
    }

    function validateForm(): boolean {
        validationErrors = {};

        if (endDate && startDate && endDate.compare(startDate) < 0) {
            validationErrors.endDate = $t('readingActivity.validationEndDateAfterStart');
        }

        const pagesReadNum = parseInt(pagesRead);
        const totalPagesNum = parseInt(totalPages);
        if (pagesRead && totalPages && !isNaN(pagesReadNum) && !isNaN(totalPagesNum) && pagesReadNum > totalPagesNum) {
            validationErrors.pagesRead = $t('readingActivity.validationPagesReadExceedsTotal');
        }

        return Object.keys(validationErrors).length === 0;
    }

    async function saveActivity() {
        if (!validateForm()) {
            return;
        }

        const apiClient = new ReadingActivitiesApiClient();

        const activityData: CreateReadingActivityData | UpdateReadingActivityData = {
            languageCode: selectedLanguage?.value || null,
            startDate: startDate ? `${startDate.year}-${startDate.month.toString().padStart(2, '0')}-${startDate.day.toString().padStart(2, '0')}` : null,
            endDate: endDate ? `${endDate.year}-${endDate.month.toString().padStart(2, '0')}-${endDate.day.toString().padStart(2, '0')}` : null,
            pagesRead: pagesRead ? parseInt(pagesRead) : null,
            totalPages: totalPages ? parseInt(totalPages) : null,
        };

        let response;
        if (editingActivity) {
            response = await apiClient.update(editingActivity.id, activityData);
        } else {
            response = await apiClient.store({
                bookId: book.uuid,
                ...activityData
            });
        }

        if (!response.ok) {
            ToastNotificationsStore.push({
                text: $t('notifications.genericError'),
                type: 'warning'
            });
            return;
        }

        open = false;
        if (onActivitySaved) {
            onActivitySaved();
        }
    }

    function closeModal() {
        open = false;
    }
</script>

<Modal bind:open={open}>
    <div class="mb-4 flex justify-between rounded-t sm:mb-5">
        <div class="text-lg text-gray-900 dark:text-white md:text-xl">
            <h3 class="font-semibold">
                {isEditing ? $t('readingActivity.editActivity') : $t('readingActivity.addActivity')}
            </h3>
        </div>
    </div>
    <form class="space-y-4" on:submit|preventDefault={saveActivity}>
        <div class="text-sm">{$t('readingActivity.description')}</div>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.startDate')}
                </label>
                <div class="w-full">
                    <DatePicker bind:value={startDate}/>
                </div>
                {#if validationErrors.startDate}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.startDate}</p>
                {/if}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    <div class="flex items-center">
                        <span>{$t('readingActivity.endDate')}</span>
                        <Tooltip.Root openDelay={0}>
                            <Tooltip.Trigger asChild let:builder>
                                <button
                                    type="button"
                                    class="ml-2 inline-flex items-center justify-center w-5 h-5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
                                    use:builder.action
                                    {...builder}
                                >
                                    <InformationCircleSvg svgClass="w-5 h-5" />
                                </button>
                            </Tooltip.Trigger>
                            <Tooltip.Content>
                                <p>{$t('readingActivity.endDateTooltip')}</p>
                            </Tooltip.Content>
                        </Tooltip.Root>
                    </div>
                </label>
                <div class="w-full">
                    <DatePicker bind:value={endDate} disabled={isEndDateDisabled}/>
                </div>
                {#if validationErrors.endDate}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.endDate}</p>
                {/if}
            </div>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {$t('readingActivity.language')}
            </label>
            <div>
                <Select title={$t('readingActivity.language')} bind:value={selectedLanguage} options={languageOptions}/>
            </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.pagesRead')}
                </label>
                <NumberInput title={$t('readingActivity.pagesRead')} bind:value={pagesRead} min={0}/>
                {#if validationErrors.pagesRead}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.pagesRead}</p>
                {/if}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {$t('readingActivity.totalPages')}
                </label>
                <NumberInput title={$t('readingActivity.totalPages')} bind:value={totalPages} min={1}/>
                {#if validationErrors.totalPages}
                    <p class="text-red-600 text-sm mt-1">{validationErrors.totalPages}</p>
                {/if}
            </div>
        </div>
        <div class="flex justify-end space-x-2 pt-4">
            <Button
                title={modalTriggeredByStatusChange ? "Skip" : "Cancel"}
                callback={closeModal}
                primary={false}
            />
            <Button title={$t('readingActivity.save')}/>
        </div>
    </form>
</Modal>
